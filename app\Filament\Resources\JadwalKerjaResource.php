<?php

namespace App\Filament\Resources;

use App\Filament\Resources\JadwalKerjaResource\Pages;
use App\Filament\Resources\JadwalKerjaResource\Widgets;
use App\Models\Schedule;
use App\Models\Karyawan;
use App\Models\Shift;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\TimePicker;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Hidden;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Actions\BulkAction;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Collection;
use Carbon\Carbon;
use App\Traits\HasExportActions;
use App\Exports\JadwalKerjaExport;
use Filament\Notifications\Notification;

class JadwalKerjaResource extends Resource
{
    use HasExportActions;

    protected static ?string $model = Schedule::class;

    protected static ?string $navigationIcon = 'heroicon-o-calendar-days';

    protected static ?string $navigationLabel = 'Jadwal Kerja';

    protected static ?string $modelLabel = 'Jadwal Kerja';

    protected static ?string $pluralModelLabel = 'Jadwal Kerja';

    protected static ?string $navigationGroup = 'Jadwal & Absensi';

    protected static ?int $navigationSort = 1;

    public static function canAccess(): bool
    {
        $user = Auth::user();
        return $user->can('view_any_schedule') ||
            $user->hasRole(['super_admin', 'manager', 'hrd_manager', 'manager_hrd', 'kepala_toko']) ||
            $user->role === 'supervisor';
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Informasi Jadwal')
                    ->schema([
                        Select::make('karyawan_id')
                            ->label('Karyawan')
                            ->options(function () {
                                $user = Auth::user();
                                $query = Karyawan::query()->with('entitas');

                                // Filter based on user role
                                if ($user->hasRole(['kepala_toko'])) {
                                    $query->where('id_entitas', $user->karyawan->id_entitas);
                                } elseif ($user->hasAnyRole(['manager_hrd'])) {
                                    // Manager HRD can see all employees from all entitas - no filtering needed
                                } elseif ($user->hasAnyRole(['supervisor'])) {
                                    $query->where('supervisor_id', $user->id);
                                }

                                return $query->get()->mapWithKeys(function ($karyawan) {
                                    return [$karyawan->id => $karyawan->nama_lengkap . ' - ' . $karyawan->entitas?->nama];
                                });
                            })
                            ->required()
                            ->searchable()
                            ->preload(),

                        DatePicker::make('tanggal_jadwal')
                            ->label('Tanggal Jadwal')
                            ->required()
                            ->default(now()),

                        Select::make('shift_id')
                            ->label('Shift')
                            ->options(Shift::where('is_active', true)->pluck('nama_shift', 'id'))
                            ->required()
                            ->searchable()
                            ->reactive()
                            ->afterStateUpdated(function ($state, callable $set) {
                                if ($state) {
                                    $shift = Shift::find($state);
                                    if ($shift) {
                                        $set('waktu_masuk', $shift->waktu_mulai);
                                        $set('waktu_keluar', $shift->waktu_selesai);
                                    }
                                }
                            }),
                    ])->columns(2),

                Section::make('Waktu Kerja')
                    ->schema([
                        TimePicker::make('waktu_masuk')
                            ->label('Waktu Masuk')
                            ->required()
                            ->seconds(false),

                        TimePicker::make('waktu_keluar')
                            ->label('Waktu Keluar')
                            ->required()
                            ->seconds(false),

                        Select::make('status')
                            ->label('Status')
                            ->options([
                                'Hadir' => 'Hadir',
                                'Libur' => 'Libur',
                                'Cuti' => 'Cuti',
                                'Izin' => 'Izin',
                                'Sakit' => 'Sakit',
                            ])
                            ->default('Hadir'),

                        Toggle::make('is_approved')
                            ->label('Disetujui')
                            ->default(true),
                    ])->columns(2),

                Section::make('Keterangan')
                    ->schema([
                        Textarea::make('keterangan')
                            ->label('Keterangan')
                            ->rows(3)
                            ->placeholder('Keterangan tambahan untuk jadwal ini'),

                        Hidden::make('supervisor_id')
                            ->default(Auth::id()),
                    ])->columns(1),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('tanggal_jadwal')
                    ->label('Tanggal')
                    ->date('d M Y')
                    ->sortable()
                    ->searchable(),

                TextColumn::make('karyawan.nama_lengkap')
                    ->label('Karyawan')
                    ->searchable()
                    ->sortable()
                    ->wrap(),

                TextColumn::make('karyawan.entitas.nama_entitas')
                    ->label('Entitas')
                    ->searchable()
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('shift.nama_shift')
                    ->label('Shift')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('waktu_masuk')
                    ->label('Masuk')
                    ->time('H:i')
                    ->sortable(),

                TextColumn::make('waktu_keluar')
                    ->label('Keluar')
                    ->time('H:i')
                    ->sortable(),

                TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'Hadir' => 'success',
                        'Libur' => 'info',
                        'Cuti' => 'warning',
                        'Izin' => 'warning',
                        'Sakit' => 'danger',
                        default => 'gray',
                    })
                    ->sortable(),

                IconColumn::make('is_approved')
                    ->label('Disetujui')
                    ->boolean()
                    ->sortable(),

                TextColumn::make('supervisor.name')
                    ->label('Dibuat Oleh')
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(),
            ])
            ->filters([
                SelectFilter::make('karyawan_id')
                    ->label('Karyawan')
                    ->options(function () {
                        $user = Auth::user();
                        $query = Karyawan::query();

                        if ($user->role === 'supervisor') {
                            $query->where('supervisor_id', $user->id);
                        }

                        return $query->pluck('nama_lengkap', 'id');
                    })
                    ->searchable(),

                SelectFilter::make('shift_id')
                    ->label('Shift')
                    ->relationship('shift', 'nama_shift'),

                SelectFilter::make('status')
                    ->options([
                        'Hadir' => 'Hadir',
                        'Libur' => 'Libur',
                        'Cuti' => 'Cuti',
                        'Izin' => 'Izin',
                        'Sakit' => 'Sakit',
                    ]),

                Filter::make('tanggal')
                    ->form([
                        DatePicker::make('dari_tanggal')
                            ->label('Dari Tanggal'),
                        DatePicker::make('sampai_tanggal')
                            ->label('Sampai Tanggal'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['dari_tanggal'],
                                fn(Builder $query, $date): Builder => $query->whereDate('tanggal_jadwal', '>=', $date),
                            )
                            ->when(
                                $data['sampai_tanggal'],
                                fn(Builder $query, $date): Builder => $query->whereDate('tanggal_jadwal', '<=', $date),
                            );
                    }),

                Filter::make('minggu_ini')
                    ->label('Minggu Ini')
                    ->query(fn(Builder $query): Builder => $query->whereBetween('tanggal_jadwal', [
                        Carbon::now()->startOfWeek(),
                        Carbon::now()->endOfWeek()
                    ])),

                Filter::make('bulan_ini')
                    ->label('Bulan Ini')
                    ->query(fn(Builder $query): Builder => $query->whereBetween('tanggal_jadwal', [
                        Carbon::now()->startOfMonth(),
                        Carbon::now()->endOfMonth()
                    ])),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),

                Tables\Actions\Action::make('duplicate')
                    ->label('Duplikasi')
                    ->icon('heroicon-o-document-duplicate')
                    ->color('info')
                    ->form([
                        DatePicker::make('tanggal_mulai')
                            ->label('Tanggal Mulai')
                            ->required()
                            ->default(Carbon::tomorrow()),
                        DatePicker::make('tanggal_selesai')
                            ->label('Tanggal Selesai')
                            ->required()
                            ->default(Carbon::tomorrow()->addDays(6)),
                    ])
                    ->action(function (Schedule $record, array $data) {
                        $startDate = Carbon::parse($data['tanggal_mulai']);
                        $endDate = Carbon::parse($data['tanggal_selesai']);
                        $created = 0;
                        $skipped = 0;

                        $currentDate = $startDate->copy();
                        while ($currentDate->lte($endDate)) {
                            $existingSchedule = Schedule::where('karyawan_id', $record->karyawan_id)
                                ->where('tanggal_jadwal', $currentDate->format('Y-m-d'))
                                ->first();

                            if (!$existingSchedule) {
                                Schedule::create([
                                    'karyawan_id' => $record->karyawan_id,
                                    'shift_id' => $record->shift_id,
                                    'supervisor_id' => $record->supervisor_id,
                                    'tanggal_jadwal' => $currentDate->format('Y-m-d'),
                                    'waktu_masuk' => $record->waktu_masuk,
                                    'waktu_keluar' => $record->waktu_keluar,
                                    'status' => $record->status,
                                    'keterangan' => $record->keterangan,
                                    'is_approved' => $record->is_approved,
                                ]);
                                $created++;
                            } else {
                                $skipped++;
                            }

                            $currentDate->addDay();
                        }

                        Notification::make()
                            ->title('Duplikasi jadwal selesai')
                            ->body("Berhasil membuat {$created} jadwal baru, {$skipped} jadwal dilewati karena sudah ada.")
                            ->success()
                            ->send();
                    })
                    ->requiresConfirmation(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),

                    BulkAction::make('approve')
                        ->label('Setujui Jadwal')
                        ->icon('heroicon-o-check-circle')
                        ->color('success')
                        ->visible(fn() => Auth::user()->hasAnyRole(['kepala_toko', 'manager_hrd', 'supervisor', 'super_admin', 'manager', 'hrd_manager']))
                        ->action(function (Collection $records) {
                            $records->each(function ($record) {
                                $record->update(['is_approved' => true]);
                            });

                            Notification::make()
                                ->title('Jadwal berhasil disetujui')
                                ->body(count($records) . ' jadwal telah berhasil disetujui.')
                                ->success()
                                ->send();
                        })
                        ->requiresConfirmation()
                        ->deselectRecordsAfterCompletion(),

                    BulkAction::make('unapprove')
                        ->label('Batalkan Persetujuan')
                        ->icon('heroicon-o-x-circle')
                        ->color('danger')
                        ->visible(fn() => Auth::user()->hasAnyRole(['kepala_toko', 'manager_hrd', 'supervisor', 'super_admin', 'manager', 'hrd_manager']))
                        ->action(function (Collection $records) {
                            $records->each(function ($record) {
                                $record->update(['is_approved' => false]);
                            });

                            Notification::make()
                                ->title('Persetujuan jadwal dibatalkan')
                                ->body(count($records) . ' jadwal telah dibatalkan persetujuannya.')
                                ->success()
                                ->send();
                        })
                        ->requiresConfirmation()
                        ->deselectRecordsAfterCompletion(),
                ]),
            ])
            ->headerActions([
                ...self::getExportActions(JadwalKerjaExport::class, 'Data Jadwal Kerja'),
            ])
            ->defaultSort('tanggal_jadwal', 'desc');
    }

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery()
            ->with(['karyawan.entitas', 'shift', 'supervisor']);

        $user = Auth::user();

        // Filter based on user role
        if ($user) {
            if ($user->hasRole(['kepala_toko'])) {
                // Kepala toko can see all schedules from their region/entitas
                $query->whereHas('karyawan', function ($q) use ($user) {
                    $q->where('id_entitas', $user->karyawan->id_entitas);
                });
            } elseif ($user->hasAnyRole(['manager_hrd'])) {
                // Manager HRD can see all schedules from all entitas - no filtering needed
                // Query remains unchanged for manager_hrd
            } elseif ($user->hasAnyRole(['supervisor'])) {
                // Supervisors can only see their supervised employees' schedules
                $employeeIds = Karyawan::where('supervisor_id', $user->id)->pluck('id')->toArray();
                $query->whereIn('karyawan_id', $employeeIds);
            }
        }

        return $query;
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getWidgets(): array
    {
        return [
            Widgets\JadwalKerjaOverview::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListJadwalKerjas::route('/'),
            'create' => Pages\CreateJadwalKerja::route('/create'),
            'view' => Pages\ViewJadwalKerja::route('/{record}'),
            'edit' => Pages\EditJadwalKerja::route('/{record}/edit'),
        ];
    }
}
