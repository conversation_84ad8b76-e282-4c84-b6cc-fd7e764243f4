<?php

namespace App\Providers\Filament;

use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Filament\Widgets;
use Filament\Navigation\MenuItem;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\AuthenticateSession;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use App\Http\Middleware\EnsureKaryawanRole;
use Filament\Navigation\NavigationItem;

class KaryawanPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->id('karyawan')
            ->path('karyawan')
            ->login()
            ->colors([
                'primary' => Color::Amber,
                'danger' => Color::Rose,
                'gray' => Color::Gray,
                'info' => Color::Blue,
                'success' => Color::Emerald,
                'warning' => Color::Orange,
            ])
            ->font('Poppins')
            ->viteTheme('resources/css/filament/karyawan/theme.css')
            ->discoverResources(in: app_path('Filament/Karyawan/Resources'), for: 'App\\Filament\\Karyawan\\Resources')
            ->discoverPages(in: app_path('Filament/Karyawan/Pages'), for: 'App\\Filament\\Karyawan\\Pages')
            ->discoverWidgets(in: app_path('Filament/Karyawan/Widgets'), for: 'App\\Filament\\Karyawan\\Widgets')
            ->pages([
                \App\Filament\Karyawan\Pages\Dashboard::class,
                \App\Filament\Karyawan\Pages\AbsensiDashboard::class,
                \App\Filament\Karyawan\Pages\PayrollDashboard::class,
                \App\Filament\Karyawan\Pages\ProfilePage::class,
            ])
            ->widgets([
                // Widget untuk Dashboard Utama
                Widgets\AccountWidget::class,
                \App\Filament\Karyawan\Widgets\KaryawanStats::class,
                \App\Filament\Karyawan\Widgets\SopWidget::class,

                // Widget untuk Dashboard Payroll
                \App\Filament\Karyawan\Widgets\PotonganOverview::class,
                \App\Filament\Karyawan\Widgets\DetailPotonganChart::class,
                \App\Filament\Karyawan\Widgets\DetailPotonganTable::class,

                // Widget untuk Dashboard Absensi
                \App\Filament\Karyawan\Widgets\AbsensiOverview::class,
                \App\Filament\Karyawan\Widgets\UpcomingSchedule::class,
                \App\Filament\Karyawan\Widgets\RecentAttendance::class,
            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
                \App\Http\Middleware\HandleSessionExpired::class,
                \App\Http\Middleware\RedirectBasedOnRole::class,
            ])
            ->authMiddleware([
                Authenticate::class,
                EnsureKaryawanRole::class,
            ])
            ->authGuard('web')
            ->brandName('PT. Viera Anugrah Pertama')
            ->favicon(asset('images/viera-logo.png'))
            ->databaseNotifications()
            ->navigation(true)
            ->sidebarCollapsibleOnDesktop()
            ->maxContentWidth('full')
            ->defaultAvatarProvider(
                \Filament\AvatarProviders\UiAvatarsProvider::class
            )
            ->navigationItems([
                NavigationItem::make('Pindah ke Admin Panel')
                    ->url(fn() => route('filament.admin.pages.dashboard'))
                    ->icon('heroicon-o-arrow-right-circle')
                    // ->visible(fn() => auth()->check() && auth()->user()->karyawan()->exists())
                    // if hasanyrole that not karyawan
                    ->visible(fn() => auth()->check() &&
                        !(auth()->user()->hasAnyRole(['karyawan'])))
                    ->sort(-1), // Negative sort to appear at the top
            ])
            ->userMenuItems([
                'admin_panel' => MenuItem::make()
                    ->label('Admin Panel')
                    ->url(fn() => route('filament.admin.pages.dashboard'))
                    ->icon('heroicon-o-cog')
                    ->visible(fn() => auth()->check() &&
                        !(auth()->user()->hasAnyRole(['karyawan'])))
            ]);
    }
}
